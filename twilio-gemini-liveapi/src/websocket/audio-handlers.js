export async function handleAudioData(
    sessionId,
    data,
    geminiSession,
    isSessionActive,
    deps,
    activeConnections,
    lifecycleManager,
    recoveryManager,
    flowType
) {
    if (geminiSession && isSessionActive && (data.audio || data.audioData)) {
        try {
            lifecycleManager.updateActivity(sessionId);
            // Standardize on 'audio' field name - prefer 'audio' over legacy 'audioData'
            const base64Audio = data.audio || data.audioData;
            await deps.sessionManager.sendBrowserAudioToGemini(sessionId, geminiSession, base64Audio);
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && connectionData.deepgramConnection && deps.transcriptionManager) {
                try {
                    const audioBuffer = Buffer.from(base64Audio, 'base64');
                    deps.transcriptionManager.sendAudioToTranscription(sessionId, audioBuffer);
                } catch (transcriptionError) {
                    console.error(`❌ [${sessionId}] Error sending audio to transcription:`, transcriptionError);
                }
            }
        } catch (error) {
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && recoveryManager && recoveryManager.needsRecovery(sessionId, activeConnections)) {
                await recoveryManager.recoverSession(sessionId, 'audio_processing_error', activeConnections);
            }
        }
    }
}

export async function handleTextMessage(sessionId, data, geminiSession, isSessionActive, deps) {
    if (geminiSession && isSessionActive && data.text) {
        try {
            await deps.sessionManager.sendTextToGemini(sessionId, geminiSession, data.text);
        } catch (textError) {
            console.error(`❌ [${sessionId}] Error sending text to Gemini:`, textError);
        }
    }
}

export async function handleTurnComplete(sessionId, geminiSession, isSessionActive, deps) {
    if (geminiSession && isSessionActive) {
        try {
            await deps.sessionManager.sendTurnComplete(sessionId, geminiSession);
        } catch (turnCompleteError) {
            console.error(`❌ [${sessionId}] Error sending turn complete to Gemini:`, turnCompleteError);
        }
    }
}
